#import "BaseJsonModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface MobileBindModel : BaseJsonModel

@property(nonatomic, strong) NSArray *data;

// 绑定手机号
+ (void)bindMobile:(NSString *)mobile
              code:(NSString *)code
           success:(success)_success
           failure:(failure)_failure;

// 改绑手机号 - 发送验证码到当前手机号
+ (void)sendChangeVerifyCode:(success)_success
                     failure:(failure)_failure;

// 改绑手机号 - 验证当前手机号验证码
+ (void)verifyCurrentMobile:(NSString *)code
                    success:(success)_success
                    failure:(failure)_failure;

@end

NS_ASSUME_NONNULL_END
