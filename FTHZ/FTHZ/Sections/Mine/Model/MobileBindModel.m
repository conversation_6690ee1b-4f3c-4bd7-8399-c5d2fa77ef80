#import "MobileBindModel.h"

@implementation MobileBindModel

+ (void)bindMobile:(NSString *)mobile
              code:(NSString *)code
           success:(success)_success
           failure:(failure)_failure {
    NSMutableDictionary *param = [NSMutableDictionary new];
    [param setObject:mobile forKey:@"mobile"];
    [param setObject:code forKey:@"code"];
    
    // 使用现有的验证码验证API，这个API可能同时处理登录和绑定
    [Http postAsynRequestWithUrl:KURLValidate_code
                          params:param
                         success:_success
                         failure:_failure];
}

+ (void)sendChangeVerifyCode:(success)_success
                     failure:(failure)_failure {
    // 使用忘记密码的验证码发送API，发送到当前绑定的手机号
    [Http getAsynRequestWithUrl:KURLGetForgotPassword
                         params:nil
                        success:_success
                        failure:_failure];
}

+ (void)verifyCurrentMobile:(NSString *)code
                    success:(success)_success
                    failure:(failure)_failure {
    NSMutableDictionary *param = [NSMutableDictionary new];
    [param setObject:code forKey:@"code"];
    
    // 使用忘记密码的验证API
    [Http postAsynRequestWithUrl:KURLPostForgotPasswordVerify
                          params:param
                         success:_success
                         failure:_failure];
}

@end
