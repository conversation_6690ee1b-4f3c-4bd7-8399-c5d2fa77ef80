#import "MobileBindModel.h"

@implementation MobileBindModel

+ (void)bindMobile:(NSString *)mobile
              code:(NSString *)code
           success:(success)_success
           failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:mobile forKey:@"mobile"];
  [param setObject:code forKey:@"code"];
  [Http postAsynRequestWithUrl:KURLPostMobileCodeVerify
                        params:param
                       success:_success
                       failure:_failure];
}

+ (void)sendChangeVerifyCode:(success)_success failure:(failure)_failure {
  [Http getAsynRequestWithUrl:KURLGetMobileCode
                       params:nil
                      success:_success
                      failure:_failure];
}

+ (void)verifyCurrentMobile:(NSString *)code
                    success:(success)_success
                    failure:(failure)_failure {
  NSMutableDictionary *param = [NSMutableDictionary new];
  [param setObject:code forKey:@"code"];
  [Http postAsynRequestWithUrl:KURLPostMobileCodeVerify
                        params:param
                       success:_success
                       failure:_failure];
}

@end
