#import "ChangeMobileVC.h"
#import "BindMobileVC.h"
#import "KingIdentifyingView.h"
#import "MobileBindModel.h"

@interface ChangeMobileVC ()

@property(nonatomic, strong) UILabel *phoneLabel;
@property(nonatomic, strong) UILabel *mobileLabel;
@property(nonatomic, strong) KingIdentifyingView *codeView;
@property(nonatomic, strong) UIButton *sendCodeBtn;
@property(nonatomic, strong) UIButton *submitBtn;
@property(nonatomic, strong) NSTimer *countdownTimer;
@property(nonatomic, assign) NSInteger countdown;
@property(nonatomic, assign) BOOL isCodeSent;
@property(nonatomic, copy) NSString *currentVerifyCode;

@end

@implementation ChangeMobileVC

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"改绑手机号";
  self.view.backgroundColor = [UIColor whiteColor];
  [self setupUI];
  [self sendVerifyCodeAutomatically];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
}

- (void)setupUI {
  // 提示标签
  self.phoneLabel = [[UILabel alloc] init];
  self.phoneLabel.text = @"验证码已发送至：";
  self.phoneLabel.font = [UIFont systemFontOfSize:16];
  self.phoneLabel.textColor = [UIColor grayColor];
  [self.view addSubview:self.phoneLabel];

  // 手机号显示标签
  self.mobileLabel = [[UILabel alloc] init];
  self.mobileLabel.text = CurrentUser.mobile ?: @"";
  self.mobileLabel.font = [UIFont systemFontOfSize:16];
  self.mobileLabel.textColor = KColor_White;
  self.mobileLabel.backgroundColor = KColor_HighBlack;
  self.mobileLabel.textAlignment = NSTextAlignmentCenter;
  self.mobileLabel.layer.cornerRadius = 4 * kMainTemp;
  self.mobileLabel.layer.masksToBounds = YES;
  [self.view addSubview:self.mobileLabel];

  // 验证码输入框
  __weak typeof(self) wSelf = self;
  self.codeView = [[KingIdentifyingView alloc]
      initWithFrame:CGRectMake(0, 0, kMainWidth, 100 * kMainTemp)
            cerCode:@"0000"
            setting:@{
              value_labelSize : @(50 * kMainTemp),
              value_labelSpace : @(11.5 * kMainTemp)
            }
           isSecure:NO];
  [self.codeView makeRebackBlock:^(NSString *value) {
    wSelf.currentVerifyCode = value;
    [wSelf verificationCode:value];
  }];
  [self.view addSubview:self.codeView];

  // 重新获取验证码按钮
  self.sendCodeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.sendCodeBtn setTitle:@"重新获取验证码" forState:UIControlStateNormal];
  [self.sendCodeBtn setTitleColor:KColor_HighBlack
                         forState:UIControlStateNormal];
  self.sendCodeBtn.titleLabel.font = [UIFont systemFontOfSize:16];
  [self.sendCodeBtn addTarget:self
                       action:@selector(sendVerifyCode)
             forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.sendCodeBtn];

  // 提交按钮
  self.submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.submitBtn setTitle:@"验证" forState:UIControlStateNormal];
  [self.submitBtn setTitleColor:[UIColor whiteColor]
                       forState:UIControlStateNormal];
  [self.submitBtn setBackgroundColor:KColor_HighBlack];
  self.submitBtn.layer.cornerRadius = 8 * kMainTemp;
  self.submitBtn.titleLabel.font = [UIFont systemFontOfSize:18];
  [self.submitBtn addTarget:self
                     action:@selector(submitButtonTapped)
           forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.submitBtn];

  [self setupConstraints];
}

- (void)setupConstraints {
  [self.phoneLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop)
        .offset(40 * kMainTemp);
    make.left.equalTo(self.view).offset(20 * kMainTemp);
  }];

  [self.mobileLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.phoneLabel.mas_right).offset(5 * kMainTemp);
    make.centerY.equalTo(self.phoneLabel);
    make.height.equalTo(@(20 * kMainTemp));
    make.width.equalTo(@(120 * kMainTemp));
  }];

  [self.codeView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.phoneLabel.mas_bottom).offset(40 * kMainTemp);
    make.left.right.equalTo(self.view);
    make.height.equalTo(@(100 * kMainTemp));
  }];

  [self.sendCodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.codeView.mas_bottom).offset(20 * kMainTemp);
    make.centerX.equalTo(self.view);
  }];

  [self.submitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom)
        .offset(-50 * kMainTemp);
    make.left.equalTo(self.view).offset(20 * kMainTemp);
    make.right.equalTo(self.view).offset(-20 * kMainTemp);
    make.height.equalTo(@(50 * kMainTemp));
  }];
}

- (void)sendVerifyCodeAutomatically {
  // 进入界面时自动发送验证码到当前绑定的手机号
  [self sendVerifyCode];
}

- (void)sendVerifyCode {
  self.sendCodeBtn.enabled = NO;
  self.countdown = 60;

  [MobileBindModel
      sendChangeVerifyCode:^(NSDictionary *resultObject) {
        MobileBindModel *model =
            [MobileBindModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          [self showToastFast:@"验证码已发送"];
          self.isCodeSent = YES;
          [self startCountdown];
        } else {
          [self showToastFast:model.msg];
          self.sendCodeBtn.enabled = YES;
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"发送失败，请重试"];
        self.sendCodeBtn.enabled = YES;
      }];
}

- (void)startCountdown {
  self.countdownTimer =
      [NSTimer scheduledTimerWithTimeInterval:1.0
                                       target:self
                                     selector:@selector(updateCountdown)
                                     userInfo:nil
                                      repeats:YES];
  [self updateCountdown];
}

- (void)updateCountdown {
  if (self.countdown > 0) {
    [self.sendCodeBtn setTitle:[NSString stringWithFormat:@"重新获取(%ld)",
                                                          (long)self.countdown]
                      forState:UIControlStateNormal];
    self.countdown--;
  } else {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;
    self.sendCodeBtn.enabled = YES;
    [self.sendCodeBtn setTitle:@"重新获取验证码" forState:UIControlStateNormal];
  }
}

- (void)verificationCode:(NSString *)code {
  // 验证码输入完成后自动提交
  if (code.length == 4) {
    [self submitButtonTapped];
  }
}

- (void)submitButtonTapped {
  if (!self.isCodeSent) {
    [self showToastFast:@"请先获取验证码"];
    return;
  }

  if (self.currentVerifyCode.length == 0) {
    [self showToastFast:@"请输入验证码"];
    return;
  }

  [MobileBindModel verifyCurrentMobile:self.currentVerifyCode
      success:^(NSDictionary *resultObject) {
        MobileBindModel *model =
            [MobileBindModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          [self showToastFast:@"验证成功"];
          // 验证成功后跳转到绑定新手机号界面
          [self navigateToBindNewMobile];
        } else {
          [self showToastFast:model.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"验证失败，请重试"];
      }];
}

- (void)navigateToBindNewMobile {
  BindMobileVC *bindVC = [[BindMobileVC alloc] init];
  bindVC.title = @"绑定新手机号";

  __weak typeof(self) wSelf = self;
  bindVC.didBindSuccess = ^{
    if (wSelf.didChangeSuccess) {
      wSelf.didChangeSuccess();
    }
    // 返回到设置界面
    [wSelf.navigationController
        popToViewController:wSelf.navigationController
                                .viewControllers[wSelf.navigationController
                                                     .viewControllers.count -
                                                 3]
                   animated:YES];
  };

  [self.navigationController pushViewController:bindVC animated:YES];
}

- (void)dealloc {
  if (self.countdownTimer) {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;
  }
}

@end
