#import "BindMobileVC.h"
#import "CommonCodeModel.h"
#import "KingIdentifyingView.h"
#import "MobileBindModel.h"

@interface BindMobileVC ()

@property(nonatomic, strong) UITextField *mobileTxd;
@property(nonatomic, strong) KingIdentifyingView *codeView;
@property(nonatomic, strong) UIButton *sendCodeBtn;
@property(nonatomic, strong) UIButton *submitBtn;
@property(nonatomic, strong) NSTimer *countdownTimer;
@property(nonatomic, assign) NSInteger countdown;
@property(nonatomic, assign) BOOL isCodeSent;
@property(nonatomic, copy) NSString *currentVerifyCode;

@end

@implementation BindMobileVC

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"绑定手机号";
  self.view.backgroundColor = [UIColor whiteColor];
  [self setupUI];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self setShowBackBtn:YES];
}

- (void)setupUI {
  self.mobileTxd = [[UITextField alloc] init];
  self.mobileTxd.placeholder = @"请输入手机号";
  self.mobileTxd.keyboardType = UIKeyboardTypeNumberPad;
  self.mobileTxd.backgroundColor = [UIColor colorWithRed:0.95
                                                   green:0.95
                                                    blue:0.95
                                                   alpha:1.0];
  self.mobileTxd.layer.cornerRadius = 8 * kMainTemp;
  self.mobileTxd.layer.masksToBounds = YES;

  UIView *leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 15, 44)];
  self.mobileTxd.leftView = leftView;
  self.mobileTxd.leftViewMode = UITextFieldViewModeAlways;
  [self.view addSubview:self.mobileTxd];

  __weak typeof(self) wSelf = self;
  self.codeView = [[KingIdentifyingView alloc]
      initWithFrame:CGRectMake(0, 0, kMainWidth, 100 * kMainTemp)
            cerCode:@"0000"
            setting:@{
              value_labelSize : @(50 * kMainTemp),
              value_labelSpace : @(11.5 * kMainTemp)
            }
           isSecure:NO];
  [self.codeView makeRebackBlock:^(NSString *value) {
    wSelf.currentVerifyCode = value;
    [wSelf verificationCode:value];
  }];
  [self.view addSubview:self.codeView];

  self.sendCodeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.sendCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
  [self.sendCodeBtn setTitleColor:[UIColor whiteColor]
                         forState:UIControlStateNormal];
  [self.sendCodeBtn setBackgroundColor:KColor_HighBlack];
  self.sendCodeBtn.layer.cornerRadius = 24 * kMainTemp;
  self.sendCodeBtn.layer.masksToBounds = YES;
  self.sendCodeBtn.titleLabel.font = [UIFont systemFontOfSize:16];
  [self.sendCodeBtn addTarget:self
                       action:@selector(sendVerifyCode)
             forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.sendCodeBtn];

  self.submitBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  [self.submitBtn setTitle:@"绑定" forState:UIControlStateNormal];
  [self.submitBtn setTitleColor:[UIColor whiteColor]
                       forState:UIControlStateNormal];
  [self.submitBtn setBackgroundColor:KColor_HighBlack];
  self.submitBtn.layer.cornerRadius = 30 * kMainTemp;
  self.submitBtn.layer.masksToBounds = YES;
  self.submitBtn.titleLabel.font = [UIFont systemFontOfSize:18];
  [self.submitBtn addTarget:self
                     action:@selector(submitButtonTapped)
           forControlEvents:UIControlEventTouchUpInside];
  [self.view addSubview:self.submitBtn];

  [self setupConstraints];
}

- (void)setupConstraints {
  [self.mobileTxd mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop)
        .offset(40 * kMainTemp);
    make.left.equalTo(self.view).offset(20 * kMainTemp);
    make.right.equalTo(self.view).offset(-20 * kMainTemp);
    make.height.equalTo(@(44 * kMainTemp));
  }];

  [self.sendCodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.mobileTxd.mas_bottom).offset(20 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(120 * kMainTemp, 48 * kMainTemp));
  }];

  [self.codeView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.sendCodeBtn.mas_bottom).offset(30 * kMainTemp);
    make.left.right.equalTo(self.view);
    make.height.equalTo(@(100 * kMainTemp));
  }];

  [self.submitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(self.view.mas_bottom).offset(-100 * kMainTemp);
    make.centerX.equalTo(self.view);
    make.size.mas_equalTo(CGSizeMake(160 * kMainTemp, 60 * kMainTemp));
  }];
}

- (void)sendVerifyCode {
  if (self.mobileTxd.text.length == 0) {
    [self showToastFast:@"请输入手机号"];
    return;
  }

  NSString *phoneRegex = @"^1[3-9]\\d{9}$";
  NSPredicate *phoneTest =
      [NSPredicate predicateWithFormat:@"SELF MATCHES %@", phoneRegex];
  if (![phoneTest evaluateWithObject:self.mobileTxd.text]) {
    [self showToastFast:@"请输入正确的手机号"];
    return;
  }

  self.sendCodeBtn.enabled = NO;
  self.countdown = 60;

  [MobileBindModel sendVerifyCodeToMobile:self.mobileTxd.text
      success:^(NSDictionary *resultObject) {
        MobileBindModel *model =
            [MobileBindModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          [self showToastFast:@"验证码已发送"];
          self.isCodeSent = YES;
          [self startCountdown];
          // 将焦点移动到验证码输入框
          [self.codeView becameFirstResponder2];
        } else {
          [self showToastFast:model.msg];
          self.sendCodeBtn.enabled = YES;
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"发送失败，请重试"];
        self.sendCodeBtn.enabled = YES;
      }];
}

- (void)startCountdown {
  self.countdownTimer =
      [NSTimer scheduledTimerWithTimeInterval:1.0
                                       target:self
                                     selector:@selector(updateCountdown)
                                     userInfo:nil
                                      repeats:YES];
  [self updateCountdown];
}

- (void)updateCountdown {
  if (self.countdown > 0) {
    [self.sendCodeBtn setTitle:[NSString stringWithFormat:@"重新获取(%ld)",
                                                          (long)self.countdown]
                      forState:UIControlStateNormal];
    self.countdown--;
  } else {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;
    self.sendCodeBtn.enabled = YES;
    [self.sendCodeBtn setTitle:@"重新获取" forState:UIControlStateNormal];
  }
}

- (void)verificationCode:(NSString *)code {
  if (code.length == 4) {
    [self submitButtonTapped];
  }
}

- (void)submitButtonTapped {
  if (!self.isCodeSent) {
    [self showToastFast:@"请先获取验证码"];
    return;
  }

  if (self.currentVerifyCode.length == 0) {
    [self showToastFast:@"请输入验证码"];
    return;
  }

  [MobileBindModel bindMobile:self.mobileTxd.text
      code:self.currentVerifyCode
      success:^(NSDictionary *resultObject) {
        MobileBindModel *model =
            [MobileBindModel mj_objectWithKeyValues:resultObject];
        if ([model.success boolValue]) {
          [self showToastFast:@"绑定成功"];
          if (self.didBindSuccess) {
            self.didBindSuccess();
          }
          dispatch_after(
              dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)),
              dispatch_get_main_queue(), ^{
                [self.navigationController popViewControllerAnimated:YES];
              });
        } else {
          [self showToastFast:model.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [self showToastFast:@"绑定失败，请重试"];
      }];
}

- (void)dealloc {
  if (self.countdownTimer) {
    [self.countdownTimer invalidate];
    self.countdownTimer = nil;
  }
}

@end
